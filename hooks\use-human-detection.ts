"use client";

import { useState, useRef, useCallback, useEffect } from "react";

export interface DetectionResult {
  faceDetected: boolean;
  poseDetected: boolean;
  confidence: number;
  boundingBox?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  landmarks?: {
    face?: Array<{ x: number; y: number }>;
    pose?: Array<{ x: number; y: number }>;
  };
}

export interface HumanDetectionHookReturn {
  isDetectionActive: boolean;
  detectionResult: DetectionResult | null;
  startDetection: (videoElement: HTMLVideoElement) => Promise<void>;
  stopDetection: () => void;
  error: string | null;
}

export function useHumanDetection(): HumanDetectionHookReturn {
  const [isDetectionActive, setIsDetectionActive] = useState(false);
  const [detectionResult, setDetectionResult] =
    useState<DetectionResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const detectionIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const videoElementRef = useRef<HTMLVideoElement | null>(null);
  const faceDetectorRef = useRef<any>(null);
  const poseDetectorRef = useRef<any>(null);

  // Initialize MediaPipe detectors
  const initializeDetectors = useCallback(async () => {
    try {
      // Dynamic import to avoid SSR issues
      const { FaceMesh } = await import("@mediapipe/face_mesh");

      // Initialize MediaPipe FaceMesh
      const faceMesh = new FaceMesh({
        locateFile: (file: string) => {
          return `https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh/${file}`;
        },
      });

      faceMesh.setOptions({
        maxNumFaces: 1,
        refineLandmarks: true,
        minDetectionConfidence: 0.5,
        minTrackingConfidence: 0.5,
      });

      faceDetectorRef.current = faceMesh;

      console.log("MediaPipe face detector initialized successfully");
    } catch (err) {
      console.error("Failed to initialize MediaPipe detectors:", err);
      setError("Failed to initialize detection models");
    }
  }, []);

  const detectHuman = useCallback(async () => {
    if (!videoElementRef.current || !faceDetectorRef.current) return;

    const video = videoElementRef.current;

    try {
      // Create a promise to handle MediaPipe results
      const detectionPromise = new Promise<any>((resolve) => {
        faceDetectorRef.current.onResults((results: any) => {
          resolve(results);
        });
      });

      // Send video frame to MediaPipe
      await faceDetectorRef.current.send({ image: video });

      // Wait for results
      const results = await detectionPromise;

      const faceDetected =
        results.multiFaceLandmarks && results.multiFaceLandmarks.length > 0;
      let confidence = 0.8; // MediaPipe doesn't provide confidence scores directly
      let boundingBox = undefined;
      let landmarks = undefined;

      if (faceDetected && results.multiFaceLandmarks[0]) {
        const faceLandmarks = results.multiFaceLandmarks[0];

        // Calculate bounding box from landmarks
        if (faceLandmarks && faceLandmarks.length > 0) {
          const xs = faceLandmarks.map(
            (landmark: any) => landmark.x * video.videoWidth
          );
          const ys = faceLandmarks.map(
            (landmark: any) => landmark.y * video.videoHeight
          );
          const minX = Math.min(...xs);
          const maxX = Math.max(...xs);
          const minY = Math.min(...ys);
          const maxY = Math.max(...ys);

          boundingBox = {
            x: minX,
            y: minY,
            width: maxX - minX,
            height: maxY - minY,
          };

          landmarks = {
            face: faceLandmarks.map((landmark: any) => ({
              x: landmark.x * video.videoWidth,
              y: landmark.y * video.videoHeight,
            })),
          };
        }
      }

      // For now, assume pose detected if face is detected
      // In a full implementation, you would use MediaPipe Pose
      const poseDetected = faceDetected;

      setDetectionResult({
        faceDetected,
        poseDetected,
        confidence,
        boundingBox,
        landmarks,
      });
      setError(null);
    } catch (err) {
      setError("Detection failed");
      console.error("Human detection error:", err);
    }
  }, []);

  const startDetection = useCallback(
    async (videoElement: HTMLVideoElement) => {
      try {
        setError(null);
        videoElementRef.current = videoElement;

        // Initialize MediaPipe detectors if not already done
        if (!faceDetectorRef.current) {
          await initializeDetectors();
        }

        setIsDetectionActive(true);

        // Start detection loop
        detectionIntervalRef.current = setInterval(detectHuman, 100); // 10 FPS detection
      } catch (err) {
        setError("Failed to start human detection");
        console.error("Detection start error:", err);
      }
    },
    [detectHuman, initializeDetectors]
  );

  const stopDetection = useCallback(() => {
    if (detectionIntervalRef.current) {
      clearInterval(detectionIntervalRef.current);
      detectionIntervalRef.current = null;
    }
    setIsDetectionActive(false);
    setDetectionResult(null);
    videoElementRef.current = null;
  }, []);

  useEffect(() => {
    return () => {
      if (detectionIntervalRef.current) {
        clearInterval(detectionIntervalRef.current);
      }
    };
  }, []);

  return {
    isDetectionActive,
    detectionResult,
    startDetection,
    stopDetection,
    error,
  };
}
