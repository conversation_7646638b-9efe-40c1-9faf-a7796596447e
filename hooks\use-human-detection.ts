"use client";

import { useState, useRef, useCallback, useEffect } from "react";

export interface DetectionResult {
  faceDetected: boolean;
  poseDetected: boolean;
  confidence: number;
  boundingBox?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  landmarks?: {
    face?: Array<{ x: number; y: number }>;
    pose?: Array<{ x: number; y: number }>;
  };
}

export interface HumanDetectionHookReturn {
  isDetectionActive: boolean;
  detectionResult: DetectionResult | null;
  startDetection: (videoElement: HTMLVideoElement) => Promise<void>;
  stopDetection: () => void;
  error: string | null;
}

export function useHumanDetection(): HumanDetectionHookReturn {
  const [isDetectionActive, setIsDetectionActive] = useState(false);
  const [detectionResult, setDetectionResult] =
    useState<DetectionResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const detectionIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const videoElementRef = useRef<HTMLVideoElement | null>(null);
  const faceDetectorRef = useRef<any>(null);
  const poseDetectorRef = useRef<any>(null);

  // Initialize TensorFlow.js detectors
  const initializeDetectors = useCallback(async () => {
    try {
      // Dynamic import to avoid SSR issues
      const tf = await import("@tensorflow/tfjs");
      const faceLandmarksDetection = await import(
        "@tensorflow-models/face-landmarks-detection"
      );

      // Initialize TensorFlow.js backend
      await tf.ready();

      // Initialize face detector using MediaPipe FaceMesh
      faceDetectorRef.current = await faceLandmarksDetection.createDetector(
        faceLandmarksDetection.SupportedModels.MediaPipeFaceMesh,
        {
          runtime: "tfjs",
          refineLandmarks: true,
        }
      );

      console.log("TensorFlow.js face detector initialized successfully");
    } catch (err) {
      console.error("Failed to initialize TensorFlow.js detectors:", err);
      setError("Failed to initialize detection models");
    }
  }, []);

  const detectHuman = useCallback(async () => {
    if (!videoElementRef.current || !faceDetectorRef.current) return;

    const video = videoElementRef.current;

    try {
      // Detect faces using TensorFlow.js FaceMesh
      const faces = await faceDetectorRef.current.estimateFaces(video);

      const faceDetected = faces && faces.length > 0;
      let confidence = 0;
      let boundingBox = undefined;
      let landmarks = undefined;

      if (faceDetected && faces[0]) {
        const face = faces[0];

        // Calculate confidence based on face detection score
        confidence = face.score || 0.8; // FaceMesh doesn't always provide score

        // Calculate bounding box from keypoints
        if (face.keypoints && face.keypoints.length > 0) {
          const xs = face.keypoints.map((kp: any) => kp.x);
          const ys = face.keypoints.map((kp: any) => kp.y);
          const minX = Math.min(...xs);
          const maxX = Math.max(...xs);
          const minY = Math.min(...ys);
          const maxY = Math.max(...ys);

          boundingBox = {
            x: minX,
            y: minY,
            width: maxX - minX,
            height: maxY - minY,
          };

          landmarks = {
            face: face.keypoints.map((kp: any) => ({ x: kp.x, y: kp.y })),
          };
        }
      }

      // For now, assume pose detected if face is detected
      // In a full implementation, you would use TensorFlow.js PoseNet
      const poseDetected = faceDetected;

      setDetectionResult({
        faceDetected,
        poseDetected,
        confidence,
        boundingBox,
        landmarks,
      });
      setError(null);
    } catch (err) {
      setError("Detection failed");
      console.error("Human detection error:", err);
    }
  }, []);

  const startDetection = useCallback(
    async (videoElement: HTMLVideoElement) => {
      try {
        setError(null);
        videoElementRef.current = videoElement;

        // Initialize MediaPipe detectors if not already done
        if (!faceDetectorRef.current) {
          await initializeDetectors();
        }

        setIsDetectionActive(true);

        // Start detection loop
        detectionIntervalRef.current = setInterval(detectHuman, 100); // 10 FPS detection
      } catch (err) {
        setError("Failed to start human detection");
        console.error("Detection start error:", err);
      }
    },
    [detectHuman, initializeDetectors]
  );

  const stopDetection = useCallback(() => {
    if (detectionIntervalRef.current) {
      clearInterval(detectionIntervalRef.current);
      detectionIntervalRef.current = null;
    }
    setIsDetectionActive(false);
    setDetectionResult(null);
    videoElementRef.current = null;
  }, []);

  useEffect(() => {
    return () => {
      if (detectionIntervalRef.current) {
        clearInterval(detectionIntervalRef.current);
      }
    };
  }, []);

  return {
    isDetectionActive,
    detectionResult,
    startDetection,
    stopDetection,
    error,
  };
}
