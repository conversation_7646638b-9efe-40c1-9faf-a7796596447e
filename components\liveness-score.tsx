"use client"

import type { LivenessScore as LivenessScoreType, VerificationResult } from "@/hooks/use-liveness-verification"
import { Progress } from "@/components/ui/progress"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, XCircle, AlertCircle, Shield, User, Activity, Clock, Eye } from "lucide-react"

interface LivenessScoreProps {
  score: LivenessScoreType
  verificationResult: VerificationResult | null
  isActive: boolean
}

const scoreCategories = [
  {
    key: "humanPresence" as keyof LivenessScoreType,
    name: "Human Presence",
    description: "Consistent human detection",
    icon: User,
  },
  {
    key: "gestureAuthenticity" as keyof LivenessScoreType,
    name: "Gesture Authenticity",
    description: "Natural gesture patterns",
    icon: Activity,
  },
  {
    key: "movementNaturalness" as keyof LivenessScoreType,
    name: "Movement Naturalness",
    description: "Natural movement patterns",
    icon: Activity,
  },
  {
    key: "temporalConsistency" as keyof LivenessScoreType,
    name: "Temporal Consistency",
    description: "Consistent timing patterns",
    icon: Clock,
  },
  {
    key: "antiSpoofing" as keyof LivenessScoreType,
    name: "Anti-Spoofing",
    description: "Depth and micro-movement analysis",
    icon: Shield,
  },
]

export function LivenessScore({ score, verificationResult, isActive }: LivenessScoreProps) {
  const getScoreColor = (value: number) => {
    if (value >= 0.8) return "text-green-600"
    if (value >= 0.6) return "text-yellow-600"
    return "text-red-600"
  }

  const getProgressColor = (value: number) => {
    if (value >= 0.8) return "bg-green-500"
    if (value >= 0.6) return "bg-yellow-500"
    return "bg-red-500"
  }

  if (!isActive) {
    return (
      <div className="text-center py-8">
        <Eye className="w-12 h-12 mx-auto mb-4 text-gray-400" />
        <p className="text-gray-500">Start verification to see liveness analysis</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Overall Score */}
      <Card
        className={`border-2 ${
          verificationResult?.isLive
            ? "border-green-200 bg-green-50"
            : verificationResult?.isLive === false
              ? "border-red-200 bg-red-50"
              : "border-gray-200"
        }`}
      >
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            {verificationResult?.isLive ? (
              <CheckCircle className="w-5 h-5 text-green-600" />
            ) : verificationResult?.isLive === false ? (
              <XCircle className="w-5 h-5 text-red-600" />
            ) : (
              <AlertCircle className="w-5 h-5 text-yellow-600" />
            )}
            Liveness Verification
          </CardTitle>
          <CardDescription>
            {verificationResult?.isLive
              ? "Live human verified"
              : verificationResult?.isLive === false
                ? "Verification failed"
                : "Analysis in progress..."}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Overall Score</span>
              <Badge variant={score.overall >= 0.7 ? "default" : score.overall >= 0.4 ? "secondary" : "destructive"}>
                {Math.round(score.overall * 100)}%
              </Badge>
            </div>
            <Progress value={score.overall * 100} className="h-3" />

            {verificationResult && (
              <div className="mt-4 p-3 rounded-lg bg-white border">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-sm font-medium">Confidence:</span>
                  <span className={`text-sm ${getScoreColor(verificationResult.confidence)}`}>
                    {Math.round(verificationResult.confidence * 100)}%
                  </span>
                </div>
                <div className="space-y-1">
                  {verificationResult.reasons.map((reason, index) => (
                    <div key={index} className="text-xs text-gray-600 flex items-center gap-1">
                      <div className="w-1 h-1 bg-gray-400 rounded-full" />
                      {reason}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Detailed Scores */}
      <div className="space-y-3">
        <h3 className="text-sm font-medium text-gray-700">Detailed Analysis</h3>
        {scoreCategories.map((category) => {
          const value = score[category.key]
          const IconComponent = category.icon

          return (
            <div key={category.key} className="bg-white rounded-lg border p-3">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <IconComponent className="w-4 h-4 text-gray-500" />
                  <span className="text-sm font-medium">{category.name}</span>
                </div>
                <span className={`text-sm font-medium ${getScoreColor(value)}`}>{Math.round(value * 100)}%</span>
              </div>
              <Progress value={value * 100} className="h-2 mb-1" />
              <p className="text-xs text-gray-500">{category.description}</p>
            </div>
          )
        })}
      </div>
    </div>
  )
}
