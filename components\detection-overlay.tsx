"use client"

import type { DetectionResult } from "@/hooks/use-human-detection"

interface DetectionOverlayProps {
  detectionResult: DetectionResult | null
  videoWidth: number
  videoHeight: number
}

export function DetectionOverlay({ detectionResult, videoWidth, videoHeight }: DetectionOverlayProps) {
  if (!detectionResult || !detectionResult.boundingBox) {
    return null
  }

  const { boundingBox, confidence, faceDetected } = detectionResult

  return (
    <div className="absolute inset-0 pointer-events-none">
      {/* Face detection bounding box */}
      <div
        className={`absolute border-2 ${faceDetected ? "border-green-400" : "border-red-400"} rounded-lg`}
        style={{
          left: `${(boundingBox.x / videoWidth) * 100}%`,
          top: `${(boundingBox.y / videoHeight) * 100}%`,
          width: `${(boundingBox.width / videoWidth) * 100}%`,
          height: `${(boundingBox.height / videoHeight) * 100}%`,
        }}
      >
        {/* Confidence indicator */}
        <div
          className={`absolute -top-8 left-0 px-2 py-1 text-xs font-medium rounded ${
            faceDetected ? "bg-green-500 text-white" : "bg-red-500 text-white"
          }`}
        >
          {faceDetected ? "Human Detected" : "No Human"} ({Math.round(confidence * 100)}%)
        </div>
      </div>

      {/* Detection status indicator */}
      <div className="absolute top-4 right-4">
        <div
          className={`flex items-center gap-2 px-3 py-2 rounded-full text-sm font-medium ${
            faceDetected
              ? "bg-green-100 text-green-800 border border-green-200"
              : "bg-red-100 text-red-800 border border-red-200"
          }`}
        >
          <div className={`w-2 h-2 rounded-full ${faceDetected ? "bg-green-500" : "bg-red-500"}`} />
          {faceDetected ? "Human Present" : "No Human Detected"}
        </div>
      </div>
    </div>
  )
}
