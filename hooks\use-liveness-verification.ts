"use client"

import { useState, useRef, useCallback, useEffect } from "react"
import type { DetectionResult } from "@/hooks/use-human-detection"
import type { GestureResult } from "@/hooks/use-gesture-detection"

export interface LivenessScore {
  overall: number
  humanPresence: number
  gestureAuthenticity: number
  movementNaturalness: number
  temporalConsistency: number
  antiSpoofing: number
}

export interface VerificationResult {
  isLive: boolean
  confidence: number
  score: LivenessScore
  reasons: string[]
  timestamp: number
}

export interface LivenessVerificationHookReturn {
  isVerificationActive: boolean
  currentScore: LivenessScore
  verificationResult: VerificationResult | null
  startVerification: () => void
  stopVerification: () => void
  updateDetectionData: (humanDetection: DetectionResult | null, gestureResults: GestureResult[]) => void
  resetVerification: () => void
  error: string | null
}

export function useLivenessVerification(): LivenessVerificationHookReturn {
  const [isVerificationActive, setIsVerificationActive] = useState(false)
  const [currentScore, setCurrentScore] = useState<LivenessScore>({
    overall: 0,
    humanPresence: 0,
    gestureAuthenticity: 0,
    movementNaturalness: 0,
    temporalConsistency: 0,
    antiSpoofing: 0,
  })
  const [verificationResult, setVerificationResult] = useState<VerificationResult | null>(null)
  const [error, setError] = useState<string | null>(null)

  const verificationIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const detectionHistoryRef = useRef<Array<{ detection: DetectionResult | null; timestamp: number }>>([])
  const gestureHistoryRef = useRef<Array<{ gestures: GestureResult[]; timestamp: number }>>([])
  const movementHistoryRef = useRef<Array<{ position: { x: number; y: number }; timestamp: number }>>([])

  const analyzeHumanPresence = useCallback(
    (detectionHistory: Array<{ detection: DetectionResult | null; timestamp: number }>) => {
      if (detectionHistory.length === 0) return 0

      const recentDetections = detectionHistory.slice(-20) // Last 2 seconds at 10fps
      const humanDetectedCount = recentDetections.filter((d) => d.detection?.faceDetected).length
      const consistencyScore = humanDetectedCount / recentDetections.length

      // Check for confidence consistency
      const confidenceScores = recentDetections
        .filter((d) => d.detection?.faceDetected)
        .map((d) => d.detection!.confidence)

      const avgConfidence =
        confidenceScores.length > 0 ? confidenceScores.reduce((sum, c) => sum + c, 0) / confidenceScores.length : 0

      return Math.min(consistencyScore * avgConfidence, 1)
    },
    [],
  )

  const analyzeGestureAuthenticity = useCallback(
    (gestureHistory: Array<{ gestures: GestureResult[]; timestamp: number }>) => {
      if (gestureHistory.length === 0) return 0

      const completedGestures = gestureHistory.flatMap((h) => h.gestures).filter((g) => g.completed)

      if (completedGestures.length === 0) return 0

      // Check for natural gesture timing and variation
      const gestureTypes = new Set(completedGestures.map((g) => g.type))
      const diversityScore = Math.min(gestureTypes.size / 6, 1) // Max 6 gesture types

      // Analyze confidence distribution
      const confidences = completedGestures.map((g) => g.confidence)
      const avgConfidence = confidences.reduce((sum, c) => sum + c, 0) / confidences.length
      const confidenceVariation = Math.sqrt(
        confidences.reduce((sum, c) => sum + Math.pow(c - avgConfidence, 2), 0) / confidences.length,
      )

      // Natural gestures should have some variation in confidence
      const naturalVariationScore = Math.min(confidenceVariation * 2, 1)

      return diversityScore * 0.4 + avgConfidence * 0.4 + naturalVariationScore * 0.2
    },
    [],
  )

  const analyzeMovementNaturalness = useCallback(
    (movementHistory: Array<{ position: { x: number; y: number }; timestamp: number }>) => {
      if (movementHistory.length < 10) return 0

      const recent = movementHistory.slice(-30) // Last 3 seconds

      // Calculate movement smoothness
      const movements = []
      for (let i = 1; i < recent.length; i++) {
        const dx = recent[i].position.x - recent[i - 1].position.x
        const dy = recent[i].position.y - recent[i - 1].position.y
        const distance = Math.sqrt(dx * dx + dy * dy)
        const timeDiff = recent[i].timestamp - recent[i - 1].timestamp
        movements.push({ distance, timeDiff })
      }

      // Natural movement should have variation but not be too erratic
      const distances = movements.map((m) => m.distance)
      const avgDistance = distances.reduce((sum, d) => sum + d, 0) / distances.length
      const distanceVariation = Math.sqrt(
        distances.reduce((sum, d) => sum + Math.pow(d - avgDistance, 2), 0) / distances.length,
      )

      // Score based on natural movement patterns
      const smoothnessScore = Math.max(0, 1 - distanceVariation / (avgDistance + 1))
      const activityScore = Math.min(avgDistance / 5, 1) // Some movement is expected

      return smoothnessScore * 0.6 + activityScore * 0.4
    },
    [],
  )

  const analyzeTemporalConsistency = useCallback(
    (
      detectionHistory: Array<{ detection: DetectionResult | null; timestamp: number }>,
      gestureHistory: Array<{ gestures: GestureResult[]; timestamp: number }>,
    ) => {
      if (detectionHistory.length < 10 || gestureHistory.length < 5) return 0

      // Check for consistent detection over time
      const timeSpan = detectionHistory[detectionHistory.length - 1].timestamp - detectionHistory[0].timestamp
      const expectedFrames = Math.floor(timeSpan / 100) // 10fps expected
      const actualFrames = detectionHistory.length
      const frameConsistency = Math.min(actualFrames / expectedFrames, 1)

      // Check for gesture timing consistency
      const gestureTimings = gestureHistory
        .flatMap((h) => h.gestures)
        .filter((g) => g.completed)
        .map((_, index, arr) => (index > 0 ? arr[index].progress - arr[index - 1].progress : 0))
        .filter((timing) => timing > 0)

      const avgGestureTiming =
        gestureTimings.length > 0 ? gestureTimings.reduce((sum, t) => sum + t, 0) / gestureTimings.length : 0

      // Natural gestures should take reasonable time (not too fast/slow)
      const timingNaturalness = avgGestureTiming > 0.1 && avgGestureTiming < 2 ? 1 : 0.5

      return frameConsistency * 0.7 + timingNaturalness * 0.3
    },
    [],
  )

  const analyzeAntiSpoofing = useCallback(
    (
      detectionHistory: Array<{ detection: DetectionResult | null; timestamp: number }>,
      movementHistory: Array<{ position: { x: number; y: number }; timestamp: number }>,
    ) => {
      if (detectionHistory.length < 20) return 0

      // Check for depth/3D movement indicators
      const recentDetections = detectionHistory.slice(-20)
      const boundingBoxes = recentDetections
        .filter((d) => d.detection?.boundingBox)
        .map((d) => d.detection!.boundingBox!)

      if (boundingBoxes.length < 10) return 0.3

      // Analyze bounding box size variations (indicates depth changes)
      const boxSizes = boundingBoxes.map((box) => box.width * box.height)
      const avgBoxSize = boxSizes.reduce((sum, size) => sum + size, 0) / boxSizes.length
      const sizeVariation = Math.sqrt(
        boxSizes.reduce((sum, size) => sum + Math.pow(size - avgBoxSize, 2), 0) / boxSizes.length,
      )

      const depthVariationScore = Math.min(sizeVariation / (avgBoxSize * 0.1), 1)

      // Check for micro-movements (natural human tremor)
      const microMovements = movementHistory
        .slice(-10)
        .map((pos, index, arr) => {
          if (index === 0) return 0
          const dx = pos.position.x - arr[index - 1].position.x
          const dy = pos.position.y - arr[index - 1].position.y
          return Math.sqrt(dx * dx + dy * dy)
        })
        .filter((movement) => movement > 0 && movement < 3) // Small natural movements

      const microMovementScore = Math.min(microMovements.length / 5, 1)

      // Check for lighting/shadow variations (indicates 3D face)
      const confidenceVariations = recentDetections
        .filter((d) => d.detection?.faceDetected)
        .map((d) => d.detection!.confidence)

      const confidenceVariation =
        confidenceVariations.length > 1
          ? Math.sqrt(
              confidenceVariations.reduce((sum, c, i, arr) => sum + (i > 0 ? Math.pow(c - arr[i - 1], 2) : 0), 0) /
                (confidenceVariations.length - 1),
            )
          : 0

      const lightingVariationScore = Math.min(confidenceVariation * 10, 1)

      return depthVariationScore * 0.4 + microMovementScore * 0.3 + lightingVariationScore * 0.3
    },
    [],
  )

  const calculateLivenessScore = useCallback(() => {
    const detectionHistory = detectionHistoryRef.current
    const gestureHistory = gestureHistoryRef.current
    const movementHistory = movementHistoryRef.current

    const humanPresence = analyzeHumanPresence(detectionHistory)
    const gestureAuthenticity = analyzeGestureAuthenticity(gestureHistory)
    const movementNaturalness = analyzeMovementNaturalness(movementHistory)
    const temporalConsistency = analyzeTemporalConsistency(detectionHistory, gestureHistory)
    const antiSpoofing = analyzeAntiSpoofing(detectionHistory, movementHistory)

    const overall =
      humanPresence * 0.25 +
      gestureAuthenticity * 0.25 +
      movementNaturalness * 0.2 +
      temporalConsistency * 0.15 +
      antiSpoofing * 0.15

    const score: LivenessScore = {
      overall,
      humanPresence,
      gestureAuthenticity,
      movementNaturalness,
      temporalConsistency,
      antiSpoofing,
    }

    setCurrentScore(score)

    // Generate verification result if score is high enough
    if (overall >= 0.7 && gestureHistory.flatMap((h) => h.gestures).filter((g) => g.completed).length >= 3) {
      const reasons = []
      if (humanPresence > 0.8) reasons.push("Consistent human presence detected")
      if (gestureAuthenticity > 0.7) reasons.push("Natural gesture patterns verified")
      if (movementNaturalness > 0.6) reasons.push("Natural movement detected")
      if (temporalConsistency > 0.7) reasons.push("Temporal consistency maintained")
      if (antiSpoofing > 0.6) reasons.push("Anti-spoofing checks passed")

      setVerificationResult({
        isLive: true,
        confidence: overall,
        score,
        reasons,
        timestamp: Date.now(),
      })
    } else if (overall < 0.3) {
      const reasons = []
      if (humanPresence < 0.5) reasons.push("Inconsistent human detection")
      if (gestureAuthenticity < 0.4) reasons.push("Unnatural gesture patterns")
      if (movementNaturalness < 0.3) reasons.push("Robotic or static movement")
      if (temporalConsistency < 0.4) reasons.push("Temporal inconsistencies detected")
      if (antiSpoofing < 0.3) reasons.push("Potential spoofing detected")

      setVerificationResult({
        isLive: false,
        confidence: 1 - overall,
        score,
        reasons,
        timestamp: Date.now(),
      })
    }
  }, [
    analyzeHumanPresence,
    analyzeGestureAuthenticity,
    analyzeMovementNaturalness,
    analyzeTemporalConsistency,
    analyzeAntiSpoofing,
  ])

  const startVerification = useCallback(() => {
    setIsVerificationActive(true)
    setError(null)
    setVerificationResult(null)

    // Start continuous verification analysis
    verificationIntervalRef.current = setInterval(calculateLivenessScore, 500) // Every 500ms
  }, [calculateLivenessScore])

  const stopVerification = useCallback(() => {
    if (verificationIntervalRef.current) {
      clearInterval(verificationIntervalRef.current)
      verificationIntervalRef.current = null
    }
    setIsVerificationActive(false)
  }, [])

  const updateDetectionData = useCallback((humanDetection: DetectionResult | null, gestureResults: GestureResult[]) => {
    const timestamp = Date.now()

    // Update detection history
    detectionHistoryRef.current.push({ detection: humanDetection, timestamp })
    if (detectionHistoryRef.current.length > 100) {
      detectionHistoryRef.current = detectionHistoryRef.current.slice(-100)
    }

    // Update gesture history
    if (gestureResults.length > 0) {
      gestureHistoryRef.current.push({ gestures: gestureResults, timestamp })
      if (gestureHistoryRef.current.length > 50) {
        gestureHistoryRef.current = gestureHistoryRef.current.slice(-50)
      }
    }

    // Update movement history (from human detection bounding box)
    if (humanDetection?.boundingBox) {
      const position = {
        x: humanDetection.boundingBox.x + humanDetection.boundingBox.width / 2,
        y: humanDetection.boundingBox.y + humanDetection.boundingBox.height / 2,
      }
      movementHistoryRef.current.push({ position, timestamp })
      if (movementHistoryRef.current.length > 100) {
        movementHistoryRef.current = movementHistoryRef.current.slice(-100)
      }
    }
  }, [])

  const resetVerification = useCallback(() => {
    setVerificationResult(null)
    setCurrentScore({
      overall: 0,
      humanPresence: 0,
      gestureAuthenticity: 0,
      movementNaturalness: 0,
      temporalConsistency: 0,
      antiSpoofing: 0,
    })
    detectionHistoryRef.current = []
    gestureHistoryRef.current = []
    movementHistoryRef.current = []
  }, [])

  useEffect(() => {
    return () => {
      if (verificationIntervalRef.current) {
        clearInterval(verificationIntervalRef.current)
      }
    }
  }, [])

  return {
    isVerificationActive,
    currentScore,
    verificationResult,
    startVerification,
    stopVerification,
    updateDetectionData,
    resetVerification,
    error,
  }
}
