"use client"

import type { Challenge, ChallengeResult } from "@/hooks/use-challenge-system"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Check<PERSON>ircle, Clock, Al<PERSON><PERSON>riangle, Target, Zap, Star } from "lucide-react"

interface ChallengeDisplayProps {
  currentChallenge: Challenge | null
  timeRemaining: number
  challengeProgress: number
  challengeResults: ChallengeResult[]
  isActive: boolean
}

const difficultyConfig = {
  easy: { color: "bg-green-100 text-green-800", icon: Target },
  medium: { color: "bg-yellow-100 text-yellow-800", icon: Zap },
  hard: { color: "bg-red-100 text-red-800", icon: Star },
}

export function ChallengeDisplay({
  currentChallenge,
  timeRemaining,
  challengeProgress,
  challengeResults,
  isActive,
}: ChallengeDisplayProps) {
  if (!isActive) {
    return (
      <div className="text-center py-8">
        <Target className="w-12 h-12 mx-auto mb-4 text-gray-400" />
        <p className="text-gray-500">Start challenge system for dynamic verification</p>
      </div>
    )
  }

  if (!currentChallenge) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4" />
        <p className="text-gray-600">Preparing next challenge...</p>
      </div>
    )
  }

  const difficultyStyle = difficultyConfig[currentChallenge.difficulty]
  const DifficultyIcon = difficultyStyle.icon

  const getTimeColor = () => {
    if (timeRemaining <= 2) return "text-red-600"
    if (timeRemaining <= 4) return "text-yellow-600"
    return "text-green-600"
  }

  const getProgressColor = () => {
    if (challengeProgress >= 0.8) return "bg-green-500"
    if (challengeProgress >= 0.5) return "bg-yellow-500"
    return "bg-blue-500"
  }

  return (
    <div className="space-y-6">
      {/* Current Challenge */}
      <Card className="border-2 border-blue-200 bg-blue-50">
        <CardContent className="p-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <DifficultyIcon className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-blue-900">Active Challenge</h3>
                <Badge className={difficultyStyle.color}>{currentChallenge.difficulty.toUpperCase()}</Badge>
              </div>
            </div>

            <div className="text-right">
              <div className={`text-2xl font-bold ${getTimeColor()}`}>{timeRemaining}s</div>
              <div className="text-sm text-gray-600 flex items-center gap-1">
                <Clock className="w-3 h-3" />
                Time left
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-4 mb-4">
            <p className="text-lg text-center font-medium text-gray-800">{currentChallenge.instruction}</p>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-blue-700">Progress</span>
              <span className="text-blue-700">{Math.round(challengeProgress * 100)}%</span>
            </div>
            <Progress value={challengeProgress * 100} className="h-3" />
            <div className="text-xs text-gray-600 text-center">
              Required accuracy: {Math.round(currentChallenge.requiredAccuracy * 100)}%
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Challenge Statistics */}
      <div className="grid grid-cols-3 gap-4">
        <div className="bg-white rounded-lg border p-4 text-center">
          <div className="text-2xl font-bold text-green-600">{challengeResults.filter((r) => r.completed).length}</div>
          <div className="text-sm text-gray-600">Completed</div>
        </div>

        <div className="bg-white rounded-lg border p-4 text-center">
          <div className="text-2xl font-bold text-red-600">{challengeResults.filter((r) => !r.completed).length}</div>
          <div className="text-sm text-gray-600">Failed</div>
        </div>

        <div className="bg-white rounded-lg border p-4 text-center">
          <div className="text-2xl font-bold text-blue-600">
            {challengeResults.length > 0
              ? Math.round((challengeResults.reduce((sum, r) => sum + r.accuracy, 0) / challengeResults.length) * 100)
              : 0}
            %
          </div>
          <div className="text-sm text-gray-600">Avg Accuracy</div>
        </div>
      </div>

      {/* Recent Results */}
      {challengeResults.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">Recent Challenges</h4>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {challengeResults
              .slice(-5)
              .reverse()
              .map((result, index) => (
                <div
                  key={result.challenge.id}
                  className={`flex items-center justify-between p-3 rounded-lg border ${
                    result.completed ? "bg-green-50 border-green-200" : "bg-red-50 border-red-200"
                  }`}
                >
                  <div className="flex items-center gap-2">
                    {result.completed ? (
                      <CheckCircle className="w-4 h-4 text-green-600" />
                    ) : (
                      <AlertTriangle className="w-4 h-4 text-red-600" />
                    )}
                    <span className="text-sm font-medium">{result.challenge.type.replace("_", " ").toUpperCase()}</span>
                    <Badge
                      variant="outline"
                      className={`text-xs ${difficultyConfig[result.challenge.difficulty].color}`}
                    >
                      {result.challenge.difficulty}
                    </Badge>
                  </div>

                  <div className="text-right">
                    <div className={`text-sm font-medium ${result.completed ? "text-green-600" : "text-red-600"}`}>
                      {Math.round(result.accuracy * 100)}%
                    </div>
                    <div className="text-xs text-gray-500">{result.timeUsed.toFixed(1)}s</div>
                  </div>
                </div>
              ))}
          </div>
        </div>
      )}
    </div>
  )
}
