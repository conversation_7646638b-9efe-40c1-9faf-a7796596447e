"use client"

import React from "react"

import type { GestureType, GestureResult } from "@/hooks/use-gesture-detection"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { CheckCircle, Circle, RotateCcw, Eye, Smile, ArrowLeft, ArrowRight } from "lucide-react"

interface GestureChallengeProps {
  currentGesture: GestureResult | null
  detectedGestures: GestureResult[]
  onRequestGesture: (gestureType: GestureType) => void
  onClearGestures: () => void
  isActive: boolean
}

const gestureConfig = {
  head_shake: {
    name: "Head Shake",
    description: "Shake your head left and right",
    icon: RotateCcw,
  },
  head_nod: {
    name: "Head Nod",
    description: "Nod your head up and down",
    icon: RotateCcw,
  },
  look_left: {
    name: "Look Left",
    description: "Turn your head to look left",
    icon: ArrowLeft,
  },
  look_right: {
    name: "Look Right",
    description: "Turn your head to look right",
    icon: <PERSON><PERSON><PERSON>,
  },
  blink: {
    name: "Blink",
    description: "Blink your eyes",
    icon: Eye,
  },
  smile: {
    name: "Smile",
    description: "Show a smile",
    icon: Smile,
  },
}

export function GestureChallenge({
  currentGesture,
  detectedGestures,
  onRequestGesture,
  onClearGestures,
  isActive,
}: GestureChallengeProps) {
  if (!isActive) {
    return (
      <div className="text-center py-8">
        <Circle className="w-12 h-12 mx-auto mb-4 text-gray-400" />
        <p className="text-gray-500">Start camera to begin gesture challenges</p>
      </div>
    )
  }

  const availableGestures: GestureType[] = ["head_shake", "head_nod", "look_left", "look_right", "blink", "smile"]

  return (
    <div className="space-y-6">
      {/* Current Challenge */}
      {currentGesture && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center gap-3 mb-3">
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
              {React.createElement(gestureConfig[currentGesture.type].icon, {
                className: "w-5 h-5 text-blue-600",
              })}
            </div>
            <div>
              <h3 className="font-semibold text-blue-900">{gestureConfig[currentGesture.type].name}</h3>
              <p className="text-sm text-blue-700">{gestureConfig[currentGesture.type].description}</p>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-blue-700">Progress</span>
              <span className="text-blue-700">{Math.round(currentGesture.progress * 100)}%</span>
            </div>
            <Progress value={currentGesture.progress * 100} className="h-2" />
          </div>

          {currentGesture.completed && (
            <div className="flex items-center gap-2 mt-3 text-green-600">
              <CheckCircle className="w-4 h-4" />
              <span className="text-sm font-medium">Gesture completed!</span>
            </div>
          )}
        </div>
      )}

      {/* Gesture Buttons */}
      <div className="grid grid-cols-2 gap-3">
        {availableGestures.map((gestureType) => {
          const config = gestureConfig[gestureType]
          const isCompleted = detectedGestures.some((g) => g.type === gestureType && g.completed)
          const isActive = currentGesture?.type === gestureType

          return (
            <Button
              key={gestureType}
              variant={isCompleted ? "default" : isActive ? "secondary" : "outline"}
              className={`h-auto p-3 flex flex-col items-center gap-2 ${
                isCompleted ? "bg-green-600 hover:bg-green-700" : ""
              }`}
              onClick={() => onRequestGesture(gestureType)}
              disabled={isActive}
            >
              <div className="flex items-center gap-2">
                {React.createElement(config.icon, { className: "w-4 h-4" })}
                {isCompleted && <CheckCircle className="w-4 h-4" />}
              </div>
              <div className="text-center">
                <div className="text-sm font-medium">{config.name}</div>
                <div className="text-xs opacity-75">{config.description}</div>
              </div>
            </Button>
          )
        })}
      </div>

      {/* Completed Gestures Summary */}
      {detectedGestures.length > 0 && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="font-semibold text-green-900 mb-2">Completed Gestures ({detectedGestures.length}/6)</h3>
          <div className="flex flex-wrap gap-2">
            {detectedGestures.map((gesture, index) => (
              <div
                key={index}
                className="flex items-center gap-1 bg-green-100 text-green-800 px-2 py-1 rounded text-sm"
              >
                <CheckCircle className="w-3 h-3" />
                {gestureConfig[gesture.type].name}
              </div>
            ))}
          </div>

          <Button onClick={onClearGestures} variant="outline" size="sm" className="mt-3 bg-transparent">
            Clear All
          </Button>
        </div>
      )}
    </div>
  )
}
