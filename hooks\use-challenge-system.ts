"use client"

import { useState, useRef, useCallback, useEffect } from "react"
import type { GestureType, GestureResult } from "@/hooks/use-gesture-detection"

export interface Challenge {
  id: string
  type: GestureType
  instruction: string
  timeLimit: number
  difficulty: "easy" | "medium" | "hard"
  requiredAccuracy: number
}

export interface ChallengeResult {
  challenge: Challenge
  completed: boolean
  accuracy: number
  timeUsed: number
  timestamp: number
}

export interface ChallengeSystemHookReturn {
  isActive: boolean
  currentChallenge: Challenge | null
  challengeResults: ChallengeResult[]
  timeRemaining: number
  challengeProgress: number
  startChallengeSystem: () => void
  stopChallengeSystem: () => void
  processGestureResult: (gestureResult: GestureResult) => void
  resetChallenges: () => void
  error: string | null
}

const challengeTemplates: Record<
  GestureType,
  Array<{ instruction: string; difficulty: "easy" | "medium" | "hard"; requiredAccuracy: number }>
> = {
  head_shake: [
    { instruction: "Shake your head left and right 3 times", difficulty: "easy", requiredAccuracy: 0.7 },
    { instruction: "Shake your head slowly left and right 4 times", difficulty: "medium", requiredAccuracy: 0.8 },
    { instruction: "Shake your head quickly left and right 5 times", difficulty: "hard", requiredAccuracy: 0.85 },
  ],
  head_nod: [
    { instruction: "Nod your head up and down 3 times", difficulty: "easy", requiredAccuracy: 0.7 },
    { instruction: "Nod your head slowly up and down 4 times", difficulty: "medium", requiredAccuracy: 0.8 },
    { instruction: "Nod your head quickly up and down 5 times", difficulty: "hard", requiredAccuracy: 0.85 },
  ],
  look_left: [
    { instruction: "Look to your left and hold for 2 seconds", difficulty: "easy", requiredAccuracy: 0.75 },
    { instruction: "Look far to your left and hold for 3 seconds", difficulty: "medium", requiredAccuracy: 0.8 },
    { instruction: "Look to your left, then center, then left again", difficulty: "hard", requiredAccuracy: 0.85 },
  ],
  look_right: [
    { instruction: "Look to your right and hold for 2 seconds", difficulty: "easy", requiredAccuracy: 0.75 },
    { instruction: "Look far to your right and hold for 3 seconds", difficulty: "medium", requiredAccuracy: 0.8 },
    { instruction: "Look to your right, then center, then right again", difficulty: "hard", requiredAccuracy: 0.85 },
  ],
  blink: [
    { instruction: "Blink your eyes 3 times slowly", difficulty: "easy", requiredAccuracy: 0.6 },
    { instruction: "Blink your eyes 5 times at normal speed", difficulty: "medium", requiredAccuracy: 0.7 },
    { instruction: "Blink your eyes 3 times, pause, then 2 more times", difficulty: "hard", requiredAccuracy: 0.8 },
  ],
  smile: [
    { instruction: "Show a natural smile for 3 seconds", difficulty: "easy", requiredAccuracy: 0.65 },
    { instruction: "Smile wide and hold for 4 seconds", difficulty: "medium", requiredAccuracy: 0.75 },
    { instruction: "Smile, relax, then smile again", difficulty: "hard", requiredAccuracy: 0.8 },
  ],
}

export function useChallengeSystem(): ChallengeSystemHookReturn {
  const [isActive, setIsActive] = useState(false)
  const [currentChallenge, setCurrentChallenge] = useState<Challenge | null>(null)
  const [challengeResults, setChallengeResults] = useState<ChallengeResult[]>([])
  const [timeRemaining, setTimeRemaining] = useState(0)
  const [challengeProgress, setChallengeProgress] = useState(0)
  const [error, setError] = useState<string | null>(null)

  const challengeTimerRef = useRef<NodeJS.Timeout | null>(null)
  const challengeStartTimeRef = useRef<number>(0)
  const usedChallengesRef = useRef<Set<string>>(new Set())

  const generateRandomChallenge = useCallback((): Challenge => {
    const gestureTypes: GestureType[] = ["head_shake", "head_nod", "look_left", "look_right", "blink", "smile"]

    // Filter out recently used gesture types to ensure variety
    const availableTypes = gestureTypes.filter((type) => {
      const recentChallenges = Array.from(usedChallengesRef.current).slice(-3)
      return !recentChallenges.some((id) => id.includes(type))
    })

    const selectedType =
      availableTypes.length > 0
        ? availableTypes[Math.floor(Math.random() * availableTypes.length)]
        : gestureTypes[Math.floor(Math.random() * gestureTypes.length)]

    const templates = challengeTemplates[selectedType]
    const template = templates[Math.floor(Math.random() * templates.length)]

    // Randomize time limit based on difficulty
    const baseTime = template.difficulty === "easy" ? 8 : template.difficulty === "medium" ? 6 : 5
    const timeLimit = baseTime + Math.floor(Math.random() * 3) // Add 0-2 seconds randomness

    const challengeId = `${selectedType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    usedChallengesRef.current.add(challengeId)

    // Keep only last 10 challenge IDs to prevent memory growth
    if (usedChallengesRef.current.size > 10) {
      const oldestId = Array.from(usedChallengesRef.current)[0]
      usedChallengesRef.current.delete(oldestId)
    }

    return {
      id: challengeId,
      type: selectedType,
      instruction: template.instruction,
      timeLimit,
      difficulty: template.difficulty,
      requiredAccuracy: template.requiredAccuracy,
    }
  }, [])

  const startNewChallenge = useCallback(() => {
    const challenge = generateRandomChallenge()
    setCurrentChallenge(challenge)
    setTimeRemaining(challenge.timeLimit)
    setChallengeProgress(0)
    challengeStartTimeRef.current = Date.now()

    // Start countdown timer
    challengeTimerRef.current = setInterval(() => {
      setTimeRemaining((prev) => {
        if (prev <= 1) {
          // Challenge timed out
          setChallengeResults((prevResults) => [
            ...prevResults,
            {
              challenge,
              completed: false,
              accuracy: 0,
              timeUsed: challenge.timeLimit,
              timestamp: Date.now(),
            },
          ])

          // Start next challenge after a brief delay
          setTimeout(() => {
            if (isActive) {
              startNewChallenge()
            }
          }, 1500)

          return 0
        }
        return prev - 1
      })
    }, 1000)
  }, [generateRandomChallenge, isActive])

  const startChallengeSystem = useCallback(() => {
    setIsActive(true)
    setError(null)
    setChallengeResults([])
    usedChallengesRef.current.clear()
    startNewChallenge()
  }, [startNewChallenge])

  const stopChallengeSystem = useCallback(() => {
    setIsActive(false)
    setCurrentChallenge(null)
    setTimeRemaining(0)
    setChallengeProgress(0)

    if (challengeTimerRef.current) {
      clearInterval(challengeTimerRef.current)
      challengeTimerRef.current = null
    }
  }, [])

  const processGestureResult = useCallback(
    (gestureResult: GestureResult) => {
      if (!currentChallenge || !isActive) return

      // Check if this gesture matches the current challenge
      if (gestureResult.type === currentChallenge.type) {
        setChallengeProgress(gestureResult.progress)

        if (gestureResult.completed && gestureResult.confidence >= currentChallenge.requiredAccuracy) {
          // Challenge completed successfully
          const timeUsed = (Date.now() - challengeStartTimeRef.current) / 1000

          setChallengeResults((prevResults) => [
            ...prevResults,
            {
              challenge: currentChallenge,
              completed: true,
              accuracy: gestureResult.confidence,
              timeUsed,
              timestamp: Date.now(),
            },
          ])

          // Clear current challenge timer
          if (challengeTimerRef.current) {
            clearInterval(challengeTimerRef.current)
            challengeTimerRef.current = null
          }

          // Start next challenge after a brief success display
          setTimeout(() => {
            if (isActive) {
              startNewChallenge()
            }
          }, 2000)
        }
      }
    },
    [currentChallenge, isActive, startNewChallenge],
  )

  const resetChallenges = useCallback(() => {
    setChallengeResults([])
    usedChallengesRef.current.clear()
    if (isActive) {
      startNewChallenge()
    }
  }, [isActive, startNewChallenge])

  useEffect(() => {
    return () => {
      if (challengeTimerRef.current) {
        clearInterval(challengeTimerRef.current)
      }
    }
  }, [])

  return {
    isActive,
    currentChallenge,
    challengeResults,
    timeRemaining,
    challengeProgress,
    startChallengeSystem,
    stopChallengeSystem,
    processGestureResult,
    resetChallenges,
    error,
  }
}
