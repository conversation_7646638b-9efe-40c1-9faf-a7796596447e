"use client"

import { useState, useRef, useCallback, useEffect } from "react"

export type GestureType = "head_shake" | "head_nod" | "look_left" | "look_right" | "blink" | "smile"

export interface GestureResult {
  type: GestureType
  confidence: number
  completed: boolean
  progress: number
}

export interface FacialLandmarks {
  leftEye: { x: number; y: number }
  rightEye: { x: number; y: number }
  nose: { x: number; y: number }
  mouth: { x: number; y: number }
  chin: { x: number; y: number }
}

export interface GestureDetectionHookReturn {
  isGestureDetectionActive: boolean
  currentGesture: GestureResult | null
  detectedGestures: GestureResult[]
  startGestureDetection: (videoElement: HTMLVideoElement) => Promise<void>
  stopGestureDetection: () => void
  requestGesture: (gestureType: GestureType) => void
  clearGestures: () => void
  error: string | null
}

export function useGestureDetection(): GestureDetectionHookReturn {
  const [isGestureDetectionActive, setIsGestureDetectionActive] = useState(false)
  const [currentGesture, setCurrentGesture] = useState<GestureResult | null>(null)
  const [detectedGestures, setDetectedGestures] = useState<GestureResult[]>([])
  const [error, setError] = useState<string | null>(null)
  const [requestedGesture, setRequestedGesture] = useState<GestureType | null>(null)

  const detectionIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const videoElementRef = useRef<HTMLVideoElement | null>(null)
  const gestureHistoryRef = useRef<Array<{ landmarks: FacialLandmarks; timestamp: number }>>([])
  const gestureProgressRef = useRef<{ [key in GestureType]: number }>({
    head_shake: 0,
    head_nod: 0,
    look_left: 0,
    look_right: 0,
    blink: 0,
    smile: 0,
  })

  const extractFacialLandmarks = useCallback((imageData: ImageData): FacialLandmarks | null => {
    // Simplified landmark extraction - in a real implementation, use MediaPipe Face Mesh
    const width = imageData.width
    const height = imageData.height
    const data = imageData.data

    // Find approximate face center by analyzing brightness patterns
    const faceCenter = { x: width / 2, y: height / 2 }

    // Simulate facial landmarks based on face center
    return {
      leftEye: { x: faceCenter.x - 30, y: faceCenter.y - 20 },
      rightEye: { x: faceCenter.x + 30, y: faceCenter.y - 20 },
      nose: { x: faceCenter.x, y: faceCenter.y },
      mouth: { x: faceCenter.x, y: faceCenter.y + 30 },
      chin: { x: faceCenter.x, y: faceCenter.y + 60 },
    }
  }, [])

  const detectGesture = useCallback(
    (landmarks: FacialLandmarks, history: Array<{ landmarks: FacialLandmarks; timestamp: number }>) => {
      if (!requestedGesture || history.length < 5) return null

      const recentHistory = history.slice(-10) // Last 10 frames
      const gestureType = requestedGesture

      switch (gestureType) {
        case "head_shake":
          return detectHeadShake(landmarks, recentHistory)
        case "head_nod":
          return detectHeadNod(landmarks, recentHistory)
        case "look_left":
          return detectLookLeft(landmarks, recentHistory)
        case "look_right":
          return detectLookRight(landmarks, recentHistory)
        case "blink":
          return detectBlink(landmarks, recentHistory)
        case "smile":
          return detectSmile(landmarks, recentHistory)
        default:
          return null
      }
    },
    [requestedGesture],
  )

  const analyzeGestures = useCallback(async () => {
    if (!videoElementRef.current) return

    const video = videoElementRef.current
    const canvas = document.createElement("canvas")
    const ctx = canvas.getContext("2d")

    if (!ctx) return

    canvas.width = video.videoWidth
    canvas.height = video.videoHeight
    ctx.drawImage(video, 0, 0)

    try {
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
      const landmarks = extractFacialLandmarks(imageData)

      if (landmarks) {
        const timestamp = Date.now()
        gestureHistoryRef.current.push({ landmarks, timestamp })

        // Keep only recent history (last 2 seconds)
        gestureHistoryRef.current = gestureHistoryRef.current.filter((entry) => timestamp - entry.timestamp < 2000)

        const gestureResult = detectGesture(landmarks, gestureHistoryRef.current)

        if (gestureResult) {
          setCurrentGesture(gestureResult)

          if (gestureResult.completed) {
            setDetectedGestures((prev) => [...prev, gestureResult])
            setRequestedGesture(null)
            setCurrentGesture(null)
            gestureProgressRef.current[gestureResult.type] = 0
          }
        }
      }

      setError(null)
    } catch (err) {
      setError("Gesture detection failed")
      console.error("Gesture detection error:", err)
    }
  }, [extractFacialLandmarks, detectGesture])

  const startGestureDetection = useCallback(
    async (videoElement: HTMLVideoElement) => {
      try {
        setError(null)
        videoElementRef.current = videoElement
        setIsGestureDetectionActive(true)

        // Start gesture analysis loop
        detectionIntervalRef.current = setInterval(analyzeGestures, 100) // 10 FPS
      } catch (err) {
        setError("Failed to start gesture detection")
        console.error("Gesture detection start error:", err)
      }
    },
    [analyzeGestures],
  )

  const stopGestureDetection = useCallback(() => {
    if (detectionIntervalRef.current) {
      clearInterval(detectionIntervalRef.current)
      detectionIntervalRef.current = null
    }
    setIsGestureDetectionActive(false)
    setCurrentGesture(null)
    setRequestedGesture(null)
    videoElementRef.current = null
    gestureHistoryRef.current = []
  }, [])

  const requestGesture = useCallback((gestureType: GestureType) => {
    setRequestedGesture(gestureType)
    setCurrentGesture({
      type: gestureType,
      confidence: 0,
      completed: false,
      progress: 0,
    })
    gestureProgressRef.current[gestureType] = 0
  }, [])

  const clearGestures = useCallback(() => {
    setDetectedGestures([])
    setCurrentGesture(null)
    setRequestedGesture(null)
  }, [])

  useEffect(() => {
    return () => {
      if (detectionIntervalRef.current) {
        clearInterval(detectionIntervalRef.current)
      }
    }
  }, [])

  return {
    isGestureDetectionActive,
    currentGesture,
    detectedGestures,
    startGestureDetection,
    stopGestureDetection,
    requestGesture,
    clearGestures,
    error,
  }
}

// Gesture detection algorithms
function detectHeadShake(
  landmarks: FacialLandmarks,
  history: Array<{ landmarks: FacialLandmarks; timestamp: number }>,
): GestureResult | null {
  if (history.length < 8) return null

  const nosePositions = history.map((h) => h.landmarks.nose.x)
  const movements = []

  for (let i = 1; i < nosePositions.length; i++) {
    movements.push(nosePositions[i] - nosePositions[i - 1])
  }

  // Detect left-right oscillation
  let oscillations = 0
  let direction = 0

  for (const movement of movements) {
    if (Math.abs(movement) > 5) {
      const newDirection = movement > 0 ? 1 : -1
      if (direction !== 0 && direction !== newDirection) {
        oscillations++
      }
      direction = newDirection
    }
  }

  const progress = Math.min(oscillations / 4, 1) // Need 4 oscillations
  const completed = oscillations >= 4

  return {
    type: "head_shake",
    confidence: completed ? 0.9 : progress * 0.7,
    completed,
    progress,
  }
}

function detectHeadNod(
  landmarks: FacialLandmarks,
  history: Array<{ landmarks: FacialLandmarks; timestamp: number }>,
): GestureResult | null {
  if (history.length < 8) return null

  const nosePositions = history.map((h) => h.landmarks.nose.y)
  const movements = []

  for (let i = 1; i < nosePositions.length; i++) {
    movements.push(nosePositions[i] - nosePositions[i - 1])
  }

  // Detect up-down oscillation
  let oscillations = 0
  let direction = 0

  for (const movement of movements) {
    if (Math.abs(movement) > 5) {
      const newDirection = movement > 0 ? 1 : -1
      if (direction !== 0 && direction !== newDirection) {
        oscillations++
      }
      direction = newDirection
    }
  }

  const progress = Math.min(oscillations / 3, 1) // Need 3 oscillations
  const completed = oscillations >= 3

  return {
    type: "head_nod",
    confidence: completed ? 0.9 : progress * 0.7,
    completed,
    progress,
  }
}

function detectLookLeft(
  landmarks: FacialLandmarks,
  history: Array<{ landmarks: FacialLandmarks; timestamp: number }>,
): GestureResult | null {
  if (history.length < 5) return null

  const recent = history.slice(-5)
  const avgNoseX = recent.reduce((sum, h) => sum + h.landmarks.nose.x, 0) / recent.length
  const initialNoseX = history[0].landmarks.nose.x

  const displacement = initialNoseX - avgNoseX
  const progress = Math.min(Math.max(displacement / 50, 0), 1)
  const completed = displacement > 40

  return {
    type: "look_left",
    confidence: completed ? 0.85 : progress * 0.6,
    completed,
    progress,
  }
}

function detectLookRight(
  landmarks: FacialLandmarks,
  history: Array<{ landmarks: FacialLandmarks; timestamp: number }>,
): GestureResult | null {
  if (history.length < 5) return null

  const recent = history.slice(-5)
  const avgNoseX = recent.reduce((sum, h) => sum + h.landmarks.nose.x, 0) / recent.length
  const initialNoseX = history[0].landmarks.nose.x

  const displacement = avgNoseX - initialNoseX
  const progress = Math.min(Math.max(displacement / 50, 0), 1)
  const completed = displacement > 40

  return {
    type: "look_right",
    confidence: completed ? 0.85 : progress * 0.6,
    completed,
    progress,
  }
}

function detectBlink(
  landmarks: FacialLandmarks,
  history: Array<{ landmarks: FacialLandmarks; timestamp: number }>,
): GestureResult | null {
  if (history.length < 3) return null

  // Simulate blink detection by analyzing eye position changes
  const eyeMovements = history.map((h) => {
    const eyeDistance = Math.abs(h.landmarks.leftEye.y - h.landmarks.rightEye.y)
    return eyeDistance
  })

  const avgEyeDistance = eyeMovements.reduce((sum, d) => sum + d, 0) / eyeMovements.length
  const minEyeDistance = Math.min(...eyeMovements)

  const blinkDetected = avgEyeDistance - minEyeDistance > 5
  const progress = blinkDetected ? 1 : 0

  return {
    type: "blink",
    confidence: blinkDetected ? 0.8 : 0.2,
    completed: blinkDetected,
    progress,
  }
}

function detectSmile(
  landmarks: FacialLandmarks,
  history: Array<{ landmarks: FacialLandmarks; timestamp: number }>,
): GestureResult | null {
  if (history.length < 3) return null

  // Simulate smile detection by analyzing mouth position
  const recent = history.slice(-3)
  const avgMouthY = recent.reduce((sum, h) => sum + h.landmarks.mouth.y, 0) / recent.length
  const initialMouthY = history[0].landmarks.mouth.y

  const mouthRaise = initialMouthY - avgMouthY
  const progress = Math.min(Math.max(mouthRaise / 10, 0), 1)
  const completed = mouthRaise > 8

  return {
    type: "smile",
    confidence: completed ? 0.75 : progress * 0.5,
    completed,
    progress,
  }
}
