"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Camera, CameraOff, Play, Square, User, RotateCcw, Zap, ZapOff } from "lucide-react"
import { useHumanDetection } from "@/hooks/use-human-detection"
import { useGestureDetection } from "@/hooks/use-gesture-detection"
import { useLivenessVerification } from "@/hooks/use-liveness-verification"
import { useChallengeSystem } from "@/hooks/use-challenge-system"
import { DetectionOverlay } from "@/components/detection-overlay"
import { GestureChallenge } from "@/components/gesture-challenge"
import { LivenessScore } from "@/components/liveness-score"
import { ChallengeDisplay } from "@/components/challenge-display"

export default function LivenessDetection() {
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const streamRef = useRef<MediaStream | null>(null)

  const [isStreaming, setIsStreaming] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [videoSize, setVideoSize] = useState({ width: 640, height: 480 })
  const [challengeMode, setChallengeMode] = useState(false)

  const {
    isDetectionActive,
    detectionResult,
    startDetection,
    stopDetection,
    error: detectionError,
  } = useHumanDetection()

  const {
    isGestureDetectionActive,
    currentGesture,
    detectedGestures,
    startGestureDetection,
    stopGestureDetection,
    requestGesture,
    clearGestures,
    error: gestureError,
  } = useGestureDetection()

  const {
    isVerificationActive,
    currentScore,
    verificationResult,
    startVerification,
    stopVerification,
    updateDetectionData,
    resetVerification,
    error: verificationError,
  } = useLivenessVerification()

  const {
    isActive: isChallengeActive,
    currentChallenge,
    challengeResults,
    timeRemaining,
    challengeProgress,
    startChallengeSystem,
    stopChallengeSystem,
    processGestureResult,
    resetChallenges,
    error: challengeError,
  } = useChallengeSystem()

  const startCamera = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 640 },
          height: { ideal: 480 },
          facingMode: "user",
        },
        audio: false,
      })

      if (videoRef.current) {
        videoRef.current.srcObject = stream
        streamRef.current = stream
        setIsStreaming(true)

        videoRef.current.onloadedmetadata = () => {
          if (videoRef.current) {
            setVideoSize({
              width: videoRef.current.videoWidth,
              height: videoRef.current.videoHeight,
            })
            // Start all detection systems
            startDetection(videoRef.current)
            startGestureDetection(videoRef.current)
            startVerification()
          }
        }
      }
    } catch (err) {
      setError("Failed to access camera. Please ensure camera permissions are granted.")
      console.error("Camera access error:", err)
    } finally {
      setIsLoading(false)
    }
  }

  const stopCamera = () => {
    // Stop all detection systems
    stopDetection()
    stopGestureDetection()
    stopVerification()
    stopChallengeSystem()

    if (streamRef.current) {
      streamRef.current.getTracks().forEach((track) => track.stop())
      streamRef.current = null
    }
    if (videoRef.current) {
      videoRef.current.srcObject = null
    }
    setIsStreaming(false)
    setChallengeMode(false)
  }

  const handleReset = () => {
    clearGestures()
    resetVerification()
    resetChallenges()
  }

  const toggleChallengeMode = () => {
    if (challengeMode) {
      stopChallengeSystem()
      setChallengeMode(false)
    } else {
      if (detectionResult?.faceDetected) {
        startChallengeSystem()
        setChallengeMode(true)
      }
    }
  }

  // Update liveness verification with detection data
  useEffect(() => {
    if (isVerificationActive) {
      updateDetectionData(detectionResult, detectedGestures)
    }
  }, [isVerificationActive, detectionResult, detectedGestures, updateDetectionData])

  // Process gesture results for challenge system
  useEffect(() => {
    if (currentGesture && isChallengeActive) {
      processGestureResult(currentGesture)
    }
  }, [currentGesture, isChallengeActive, processGestureResult])

  useEffect(() => {
    return () => {
      // Cleanup all detection systems on unmount
      stopDetection()
      stopGestureDetection()
      stopVerification()
      stopChallengeSystem()
      if (streamRef.current) {
        streamRef.current.getTracks().forEach((track) => track.stop())
      }
    }
  }, [stopDetection, stopGestureDetection, stopVerification, stopChallengeSystem])

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">Liveness Detection</h1>
          <p className="text-lg text-gray-600">Verify your identity through real-time gesture detection</p>
        </div>

        <div className="grid lg:grid-cols-3 gap-6">
          {/* Video Feed */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Camera className="w-5 h-5" />
                Live Video Feed
              </CardTitle>
              <CardDescription>Position yourself in front of the camera for liveness verification</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="relative bg-black rounded-lg overflow-hidden aspect-video">
                <video ref={videoRef} autoPlay playsInline muted className="w-full h-full object-cover" />

                {isStreaming && isDetectionActive && (
                  <DetectionOverlay
                    detectionResult={detectionResult}
                    videoWidth={videoSize.width}
                    videoHeight={videoSize.height}
                  />
                )}

                {!isStreaming && (
                  <div className="absolute inset-0 flex items-center justify-center bg-gray-900">
                    <div className="text-center text-white">
                      <Camera className="w-12 h-12 mx-auto mb-2 opacity-50" />
                      <p>Camera not active</p>
                    </div>
                  </div>
                )}
                <canvas ref={canvasRef} className="hidden" />
              </div>

              <div className="flex gap-2 mt-4">
                {!isStreaming ? (
                  <Button onClick={startCamera} disabled={isLoading} className="flex-1">
                    <Play className="w-4 h-4 mr-2" />
                    {isLoading ? "Starting..." : "Start Camera"}
                  </Button>
                ) : (
                  <>
                    <Button onClick={stopCamera} variant="destructive" className="flex-1">
                      <Square className="w-4 h-4 mr-2" />
                      Stop Camera
                    </Button>
                    <Button
                      onClick={toggleChallengeMode}
                      variant={challengeMode ? "secondary" : "default"}
                      disabled={!detectionResult?.faceDetected}
                    >
                      {challengeMode ? <ZapOff className="w-4 h-4 mr-2" /> : <Zap className="w-4 h-4 mr-2" />}
                      {challengeMode ? "Stop Challenges" : "Start Challenges"}
                    </Button>
                    <Button onClick={handleReset} variant="outline">
                      <RotateCcw className="w-4 h-4 mr-2" />
                      Reset
                    </Button>
                  </>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Liveness Score */}
          <Card>
            <CardHeader>
              <CardTitle>Liveness Analysis</CardTitle>
              <CardDescription>Real-time verification scoring</CardDescription>
            </CardHeader>
            <CardContent>
              <LivenessScore
                score={currentScore}
                verificationResult={verificationResult}
                isActive={isVerificationActive}
              />
            </CardContent>
          </Card>

          {/* Dynamic Content Area */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>{challengeMode ? "Dynamic Challenges" : "Manual Gestures"}</CardTitle>
              <CardDescription>
                {challengeMode
                  ? "Complete randomized challenges for enhanced verification"
                  : "Select and perform gestures manually"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!isStreaming ? (
                <div className="text-center py-8">
                  <CameraOff className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                  <p className="text-gray-500">Start your camera to begin verification</p>
                </div>
              ) : !detectionResult?.faceDetected ? (
                <div className="text-center py-8">
                  <User className="w-12 h-12 mx-auto mb-4 text-yellow-500" />
                  <p className="text-yellow-600 font-medium">Looking for human...</p>
                  <p className="text-sm text-gray-500">Position yourself in front of the camera</p>
                </div>
              ) : challengeMode ? (
                <ChallengeDisplay
                  currentChallenge={currentChallenge}
                  timeRemaining={timeRemaining}
                  challengeProgress={challengeProgress}
                  challengeResults={challengeResults}
                  isActive={isChallengeActive}
                />
              ) : (
                <GestureChallenge
                  currentGesture={currentGesture}
                  detectedGestures={detectedGestures}
                  onRequestGesture={requestGesture}
                  onClearGestures={clearGestures}
                  isActive={isGestureDetectionActive && detectionResult?.faceDetected}
                />
              )}
            </CardContent>
          </Card>
        </div>

        {/* Show all errors from different systems */}
        {(error || detectionError || gestureError || verificationError || challengeError) && (
          <Alert className="mt-6" variant="destructive">
            <AlertDescription>
              {error || detectionError || gestureError || verificationError || challengeError}
            </AlertDescription>
          </Alert>
        )}
      </div>
    </div>
  )
}
